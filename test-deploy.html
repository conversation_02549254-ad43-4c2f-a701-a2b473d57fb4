<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Goat Goat Admin Panel - Test Deploy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .success {
            background: rgba(34, 197, 94, 0.3);
        }
        .error {
            background: rgba(239, 68, 68, 0.3);
        }
        button {
            background: #059669;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background: #047857;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐐 Goat Goat Admin Panel - Deployment Test</h1>
        
        <div class="status success">
            <h3>✅ Basic HTML/CSS Loading</h3>
            <p>This page loaded successfully, indicating basic hosting is working.</p>
        </div>
        
        <div class="status" id="jsStatus">
            <h3>⏳ JavaScript Execution</h3>
            <p>Testing JavaScript execution...</p>
        </div>
        
        <div class="status" id="fetchStatus">
            <h3>⏳ Network Requests</h3>
            <p>Testing network connectivity...</p>
        </div>
        
        <div class="status" id="supabaseStatus">
            <h3>⏳ Supabase Connection</h3>
            <p>Testing Supabase connectivity...</p>
        </div>
        
        <button onclick="testFlutterResources()">Test Flutter Resources</button>
        <button onclick="testSupabase()">Test Supabase</button>
        <button onclick="window.location.href='/'">Go to Admin Panel</button>
        
        <div id="results"></div>
    </div>

    <script>
        // Test JavaScript execution
        document.getElementById('jsStatus').className = 'status success';
        document.getElementById('jsStatus').innerHTML = '<h3>✅ JavaScript Execution</h3><p>JavaScript is working correctly.</p>';
        
        // Test basic fetch
        async function testBasicFetch() {
            try {
                const response = await fetch('/manifest.json');
                if (response.ok) {
                    document.getElementById('fetchStatus').className = 'status success';
                    document.getElementById('fetchStatus').innerHTML = '<h3>✅ Network Requests</h3><p>Basic fetch requests are working.</p>';
                } else {
                    throw new Error('Fetch failed');
                }
            } catch (error) {
                document.getElementById('fetchStatus').className = 'status error';
                document.getElementById('fetchStatus').innerHTML = '<h3>❌ Network Requests</h3><p>Error: ' + error.message + '</p>';
            }
        }
        
        // Test Supabase connection
        async function testSupabase() {
            try {
                const response = await fetch('https://oaynfzqjielnsipttzbs.supabase.co/rest/v1/', {
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9heW5menFqaWVsbnNpcHR0emJzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzI2MjU4NzEsImV4cCI6MjA0ODIwMTg3MX0.VYyJqUOHlnyx7Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8'
                    }
                });
                
                if (response.ok) {
                    document.getElementById('supabaseStatus').className = 'status success';
                    document.getElementById('supabaseStatus').innerHTML = '<h3>✅ Supabase Connection</h3><p>Supabase is accessible.</p>';
                } else {
                    throw new Error('Supabase connection failed');
                }
            } catch (error) {
                document.getElementById('supabaseStatus').className = 'status error';
                document.getElementById('supabaseStatus').innerHTML = '<h3>❌ Supabase Connection</h3><p>Error: ' + error.message + '</p>';
            }
        }
        
        // Test Flutter resources
        async function testFlutterResources() {
            const results = document.getElementById('results');
            results.innerHTML = '<h3>Testing Flutter Resources...</h3>';
            
            const resources = [
                '/flutter_bootstrap.js',
                '/main.dart.js',
                '/flutter.js'
            ];
            
            for (const resource of resources) {
                try {
                    const response = await fetch(resource, { method: 'HEAD' });
                    const status = response.ok ? '✅' : '❌';
                    results.innerHTML += `<p>${status} ${resource} - ${response.status}</p>`;
                } catch (error) {
                    results.innerHTML += `<p>❌ ${resource} - Error: ${error.message}</p>`;
                }
            }
        }
        
        // Run initial tests
        testBasicFetch();
        setTimeout(testSupabase, 1000);
    </script>
</body>
</html>
