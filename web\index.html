<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Goat Goat Admin Panel - Meat Shop Management System">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Goat Goat Admin">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>Goat Goat Admin Panel</title>
  <link rel="manifest" href="manifest.json">

  <!-- Preload critical resources -->
  <link rel="preload" href="main.dart.js" as="script" crossorigin="anonymous">
  <link rel="preload" href="flutter_bootstrap.js" as="script" crossorigin="anonymous">

  <!-- Preload fonts to prevent CSP issues -->
  <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- Loading indicator styles -->
  <style>
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #065f46 0%, #047857 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      color: white;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 18px;
      margin-top: 20px;
      text-align: center;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Hide loading when Flutter is ready */
    .flutter-ready .loading-container {
      display: none;
    }
  </style>
</head>
<body>
  <!-- Loading indicator -->
  <div class="loading-container" id="loading">
    <div class="loading-spinner"></div>
    <div class="loading-text">Loading Goat Goat Admin Panel...</div>
  </div>

  <!-- Flutter app container -->
  <div id="flutter-app"></div>

  <script>
    // Enhanced error handling and loading management
    window.addEventListener('error', function(e) {
      console.error('Global error:', e.error);
      // Keep loading indicator visible on error
    });

    window.addEventListener('unhandledrejection', function(e) {
      console.error('Unhandled promise rejection:', e.reason);
    });

    // Hide loading indicator when Flutter is ready
    window.addEventListener('flutter-first-frame', function() {
      document.body.classList.add('flutter-ready');
      setTimeout(() => {
        const loading = document.getElementById('loading');
        if (loading) loading.style.display = 'none';
      }, 500);
    });

    // Fallback: hide loading after 10 seconds
    setTimeout(() => {
      const loading = document.getElementById('loading');
      if (loading && !document.body.classList.contains('flutter-ready')) {
        loading.innerHTML = '<div class="loading-text">Loading taking longer than expected...<br>Please check your connection.</div>';
      }
    }, 10000);
  </script>

  <!-- Register Firebase Messaging Service Worker -->
  <script>
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/firebase-messaging-sw.js')
        .then(function(registration) {
          console.log('Firebase SW registered: ', registration);
        })
        .catch(function(registrationError) {
          console.log('Firebase SW registration failed: ', registrationError);
        });
    }
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
