# Admin Panel Local Development Environment Configuration
# This file contains environment variables for local development of the admin panel

# Supabase Configuration (same as mobile app)
SUPABASE_URL=https://oaynfzqjielnsipttzbs.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9heW5menFqaWVsbnNpcHR0emJzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzI2MjU4NzEsImV4cCI6MjA0ODIwMTg3MX0.VYyJqUOHlnyx7Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8

# Admin Panel Configuration
ADMIN_URL=http://localhost:8080
ADMIN_PORT=8080
ADMIN_ENVIRONMENT=development

# Security Configuration
ENABLE_MFA=false
MAX_LOGIN_ATTEMPTS=5
SESSION_TIMEOUT_HOURS=8

# Feature Flags
ENABLE_REVIEW_MODERATION=true
ENABLE_NOTIFICATION_MANAGEMENT=true
ENABLE_USER_MANAGEMENT=true
ENABLE_ANALYTICS=true

# Development Settings
ENABLE_DEBUG_LOGGING=true
ENABLE_HOT_RELOAD=true
AUTO_REFRESH_INTERVAL=30

# Fast2SMS Configuration (same as mobile app)
FAST2SMS_API_KEY=TBXtyM2OVn0ra5SPdRCH48pghNkzm3w1xFoKIsYJGDEeb7Lvl6wShBusoREfqr0kO3M5jJdexvGQctbn
