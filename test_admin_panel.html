<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #218838; }
        .loading { opacity: 0.6; pointer-events: none; }
        #console { 
            background: #000; 
            color: #0f0; 
            padding: 10px; 
            border-radius: 4px; 
            font-family: monospace; 
            height: 200px; 
            overflow-y: auto; 
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🐐 Goat Goat Admin Panel Test Suite</h1>
    
    <div class="test-container">
        <h2>🔍 Basic Connectivity Tests</h2>
        <button onclick="testAdminPanelLoad()">Test Admin Panel Load</button>
        <button onclick="testSupabaseConnection()">Test Supabase Connection</button>
        <button onclick="testCSPHeaders()">Test CSP Headers</button>
        <button onclick="runAllTests()">Run All Tests</button>
        <div id="basic-results"></div>
    </div>

    <div class="test-container">
        <h2>📊 Test Results</h2>
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>🖥️ Console Output</h2>
        <div id="console"></div>
    </div>

    <script>
        const results = document.getElementById('test-results');
        const console_output = document.getElementById('console');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            console_output.textContent += `[${timestamp}] ${message}\n`;
            console_output.scrollTop = console_output.scrollHeight;
            
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = `[${timestamp}] ${message}`;
            results.appendChild(div);
        }

        async function testAdminPanelLoad() {
            log('🔄 Testing admin panel load...', 'info');
            try {
                const response = await fetch('https://goatgoat.info', {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const text = await response.text();
                    if (text.includes('Goat Goat Admin Panel')) {
                        log('✅ Admin panel loads successfully', 'success');
                        log(`📄 Response status: ${response.status}`, 'info');
                        log(`📏 Content length: ${text.length} characters`, 'info');
                    } else {
                        log('⚠️ Admin panel loads but content may be incorrect', 'warning');
                    }
                } else {
                    log(`❌ Admin panel failed to load: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function testSupabaseConnection() {
            log('🔄 Testing Supabase connection...', 'info');
            try {
                const response = await fetch('https://oaynfzqjielnsipttzbs.supabase.co/rest/v1/', {
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9heW5menFqaWVsbnNpcHR0emJzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MDU3NDUsImV4cCI6MjA2NTQ4MTc0NX0.RnhpZ7ri3Nf3vmDMCmLqYnB8cRNZc_u-3dhCutpUWEA'
                    }
                });
                
                if (response.ok) {
                    log('✅ Supabase connection successful', 'success');
                } else {
                    log(`❌ Supabase connection failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ Supabase connection error: ${error.message}`, 'error');
            }
        }

        async function testCSPHeaders() {
            log('🔄 Testing CSP headers...', 'info');
            try {
                const response = await fetch('https://goatgoat.info', {
                    method: 'HEAD'
                });
                
                const csp = response.headers.get('content-security-policy');
                if (csp) {
                    log('✅ CSP header found', 'success');
                    log(`📋 CSP: ${csp.substring(0, 100)}...`, 'info');
                    
                    // Check for Flutter-specific CSP requirements
                    if (csp.includes('unsafe-inline') && csp.includes('unsafe-eval')) {
                        log('✅ CSP allows Flutter web requirements', 'success');
                    } else {
                        log('⚠️ CSP may be too restrictive for Flutter web', 'warning');
                    }
                } else {
                    log('⚠️ No CSP header found', 'warning');
                }
            } catch (error) {
                log(`❌ CSP test error: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            log('🚀 Running all tests...', 'info');
            results.innerHTML = '';
            console_output.textContent = '';
            
            await testAdminPanelLoad();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testSupabaseConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testCSPHeaders();
            
            log('🎉 All tests completed!', 'success');
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            log('🔧 Admin Panel Test Suite Initialized', 'info');
            log('🌐 Target URL: https://goatgoat.info', 'info');
            log('📅 Test Date: ' + new Date().toLocaleString(), 'info');
        });
    </script>
</body>
</html>
