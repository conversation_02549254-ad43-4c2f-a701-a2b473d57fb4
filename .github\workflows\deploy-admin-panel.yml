name: Deploy Admin Panel to Netlify

on:
  push:
    branches: [ main ]
    paths:
      - 'lib/admin/**'
      - 'lib/main_admin.dart'
      - 'pubspec.yaml'
      - 'netlify.toml'
      - '.github/workflows/deploy-admin-panel.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'lib/admin/**'
      - 'lib/main_admin.dart'
      - 'pubspec.yaml'
      - 'netlify.toml'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
        cache: true
        
    - name: Get Flutter dependencies
      run: flutter pub get
      
    - name: Analyze Flutter code
      run: flutter analyze lib/main_admin.dart lib/admin/
      
    - name: Test admin panel (if tests exist)
      run: |
        if [ -d "test/admin" ]; then
          flutter test test/admin/
        else
          echo "No admin tests found, skipping..."
        fi
      
    - name: Build Flutter Web for Admin Panel
      run: |
        flutter config --enable-web
        flutter build web --target=lib/main_admin.dart --release --base-href=/
        
    - name: Verify build output
      run: |
        echo "Checking build output..."
        ls -la build/web/
        echo "Checking critical files..."
        test -f build/web/index.html && echo "✅ index.html exists"
        test -f build/web/main.dart.js && echo "✅ main.dart.js exists"
        test -f build/web/flutter_bootstrap.js && echo "✅ flutter_bootstrap.js exists"
        
    - name: Deploy to Netlify (Production)
      if: github.ref == 'refs/heads/main' && github.event_name == 'push'
      uses: netlify/actions/cli@master
      with:
        args: deploy --prod --dir=build/web --message="Admin panel deployment from GitHub Actions"
      env:
        NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
        NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
        
    - name: Deploy to Netlify (Preview)
      if: github.event_name == 'pull_request'
      uses: netlify/actions/cli@master
      with:
        args: deploy --dir=build/web --message="Admin panel preview from PR #${{ github.event.number }}"
      env:
        NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
        NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
        
    - name: Comment PR with preview URL
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const { data: deployments } = await github.rest.repos.listDeployments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            ref: context.payload.pull_request.head.sha
          });
          
          if (deployments.length > 0) {
            const deployment = deployments[0];
            const previewUrl = `https://deploy-preview-${context.payload.pull_request.number}--goatgoat.netlify.app`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 **Admin Panel Preview Deployed!**\n\n📱 Preview URL: ${previewUrl}\n\n✅ This preview includes all admin panel changes from this PR.`
            });
          }
          
    - name: Notify on success
      if: success() && github.ref == 'refs/heads/main'
      run: |
        echo "🎉 Admin panel successfully deployed to production!"
        echo "🌐 URL: https://goatgoat.info"
        echo "📊 Check deployment status at: https://app.netlify.com/sites/goatgoat/deploys"
        
    - name: Notify on failure
      if: failure()
      run: |
        echo "❌ Admin panel deployment failed!"
        echo "📋 Check the logs above for details"
        echo "🔧 Common issues:"
        echo "   - Flutter build errors"
        echo "   - Missing Netlify secrets"
        echo "   - Network connectivity issues"
