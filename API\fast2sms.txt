Overview
Bulk SMS, DLT SMS, OTP SMS API. Our Bulk SMS API work with PHP, JAVA, C#, C, Python, Ruby, Javascript, NodeJS, etc. Secure, robust and easy to integrate APIs to send DLT Approved SMS, Promotional SMS, Service Implicit SMS, Service Explicit SMS via REST API. Check Bulk SMS Price here.

Authorization Key
Fast2SMS expects for the API Key to be included in all API requests to the server in a header for POST requests & in query parameters for GET requests.
Get Your API Authorization Key from Fast2SMS Dev API section for FREE which you need to add in each API request as following:

GET https://www.fast2sms.com/dev/wallet?authorization=YOUR_API_KEY

POST authorization: YOUR_API_KEY

 You must replace YOUR_API_KEY with your Fast2SMS account API Authorization Key which look like this: "weBQKBrtZzLnD2ZUEnUYJIO40zZGnjgZm3BA1SAUd0qZ56gHm0k3X45DWR9c"



OTP Message API
You can use OTP Message API for sending Numeric based OTP Message.

In this route you can pass OTP value & Fast2SMS will deliver your Message as:
"{#var#} is your verification code."

NOTE: If you want to use your DLT Approved Custom Sender ID & Custom Message Text then use DLT SMS API.

GET Method
Following are the parameter to be used for GET API:

HTTP Request
GET https://www.fast2sms.com/dev/bulkV2

 You must replace YOUR_API_KEY with your Fast2SMS account API Authorization Key.
Body
Parameter	Required	Description
authorization	true	Provide "YOUR_API_KEY". Sign up for API Key
variables_values	true	Pass OTP value like: "5599"
(only numeric value is allowed upto 8 digit)
Your SMS will be delivered as: Your OTP: 5599
route	true	For OTP Message use "otp"
numbers	true	You can send multiple mobile numbers seperated by comma like: "**********,**********,**********"
flash	false	This field is optional, it will use "0" as default value or you can set to "1" for sending flash message.

POST Method
Following are the parameter to be used for POST API:

HTTP Request
POST https://www.fast2sms.com/dev/bulkV2

Headers
Parameter	Required	Description
authorization	true	Provide "YOUR_API_KEY". Sign up for API Key
variables_values	true	Pass OTP value like: "5599"
(only numeric value is allowed upto 8 digit)
Your SMS will be delivered as: Your OTP: 5599
route	true	For OTP Message use "otp"
numbers	true	You can send multiple mobile numbers seperated by comma like: "**********,**********,**********"
flash	false	This field is optional, it will use "0" as default value or you can set to "1" for sending flash message.