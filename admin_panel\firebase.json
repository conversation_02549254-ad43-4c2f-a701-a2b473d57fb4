{"hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**", "headers": [{"key": "Content-Security-Policy", "value": "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://fonts.gstatic.com https://www.gstatic.com https://fonts.googleapis.com https://oaynfzqjielnsipttzbs.supabase.co data: blob:; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.gstatic.com; font-src 'self' https://fonts.gstatic.com https://fonts.googleapis.com data: blob:; img-src 'self' data: https: blob:; connect-src 'self' https://oaynfzqjielnsipttzbs.supabase.co wss://oaynfzqjielnsipttzbs.supabase.co https://fonts.gstatic.com https://www.gstatic.com https://fonts.googleapis.com; worker-src 'self' blob: data:; child-src 'self' blob: data:; object-src 'self' data: blob:; base-uri 'self'; frame-ancestors 'none'; manifest-src 'self';"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}]}]}}